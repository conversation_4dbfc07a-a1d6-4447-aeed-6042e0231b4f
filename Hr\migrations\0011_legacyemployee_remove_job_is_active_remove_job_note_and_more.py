# Generated by Django 5.0.14 on 2025-07-14 16:21

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('Hr', '0010_add_legacy_employee_model'),
    ]

    operations = [
        migrations.CreateModel(
            name='LegacyEmployee',
            fields=[
                ('emp_id', models.IntegerField(db_column='Emp_ID', primary_key=True, serialize=False, verbose_name='رقم الموظف')),
                ('emp_first_name', models.Char<PERSON>ield(blank=True, db_column='Emp_First_Name', max_length=50, null=True, verbose_name='الاسم الأول')),
                ('emp_second_name', models.CharField(blank=True, db_column='Emp_Second_Name', max_length=50, null=True, verbose_name='الاسم الثاني')),
                ('emp_full_name', models.CharField(blank=True, db_column='Emp_Full_Name', max_length=100, null=True, verbose_name='الاسم الكامل')),
                ('working_condition', models.CharField(blank=True, choices=[('سارى', 'سارى'), ('إجازة', 'إجازة'), ('استقالة', 'استقالة'), ('انقطاع عن العمل', 'انقطاع عن العمل')], db_column='Working_Condition', max_length=50, null=True, verbose_name='حالة العمل')),
                ('insurance_status', models.CharField(blank=True, choices=[('مؤمن عليه', 'مؤمن عليه'), ('غير مؤمن عليه', 'غير مؤمن عليه')], db_column='Insurance_Status', max_length=50, null=True, verbose_name='حالة التأمين')),
                ('national_id', models.CharField(blank=True, db_column='National_ID', max_length=14, null=True, verbose_name='الرقم القومي')),
                ('date_birth', models.DateField(blank=True, db_column='Date_Birth', null=True, verbose_name='تاريخ الميلاد')),
                ('emp_date_hiring', models.DateField(blank=True, db_column='Emp_Date_Hiring', null=True, verbose_name='تاريخ التوظيف')),
                ('dept_name', models.CharField(blank=True, db_column='Dept_Name', max_length=50, null=True, verbose_name='اسم القسم')),
                ('jop_code', models.IntegerField(blank=True, db_column='Jop_Code', null=True, verbose_name='كود الوظيفة')),
            ],
            options={
                'verbose_name': 'الموظف',
                'verbose_name_plural': 'الموظفون',
                'db_table': 'Tbl_Employee',
                'managed': False,
            },
        ),
        migrations.RemoveField(
            model_name='job',
            name='is_active',
        ),
        migrations.RemoveField(
            model_name='job',
            name='note',
        ),
        migrations.AddField(
            model_name='job',
            name='department',
            field=models.ForeignKey(blank=True, db_column='Dept_Code', null=True, on_delete=django.db.models.deletion.SET_NULL, to='Hr.legacydepartment', verbose_name='القسم'),
        ),
        migrations.AlterField(
            model_name='job',
            name='jop_code',
            field=models.IntegerField(db_column='Jop_Code', primary_key=True, serialize=False, verbose_name='رمز الوظيفة'),
        ),
        migrations.AlterField(
            model_name='job',
            name='jop_name',
            field=models.CharField(db_column='Jop_Name', max_length=50, verbose_name='اسم الوظيفة'),
        ),
        migrations.AlterModelTable(
            name='job',
            table='Tbl_Jop',
        ),
    ]
